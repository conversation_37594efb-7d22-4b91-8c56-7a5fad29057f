using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using FairyGUI;
using LitJson;
using UnityEngine;
using UnityEngine.Networking;

public class LogReport : MonoBehaviour
{
    // private static string url = "https://game-yrkp.cn-shanghai.log.aliyuncs.com/logstores/client/track?APIVersion=0.6.0";
    private static string postUrl = "https://game-yrkp.cn-shanghai.log.aliyuncs.com/logstores/client/track";
    // private static StringBuilder sb = new StringBuilder();

    //最近的点击对象
    private static List<string> touchTargets = new List<string>();


    public static LogReport Inst;
    public bool enable = true;
    /// <summary>
    /// 每分钟最多可上报几条
    /// </summary>
    public int maxLogsPerMinute = 12;
    /// <summary>
    /// 总共最多可上报几条
    /// </summary>
    public int maxLogCount = 10;
    /// <summary>
    /// 收集最近的点击对象上限
    /// </summary>
    public int maxTouchTargets = 10;
    private int curTouchTarget;

    private float lastLogTime;
    private int logCount = 0;
    private void Awake()
    {
        Inst = this;

#if UNITY_EDITOR
        return;
#endif

        Application.logMessageReceived += LogCallback;
        GRoot.inst.onTouchBegin.Add(OnGlobalTouchBegin);
    }

    private static readonly string splitTag1 = "->";
    private static readonly string splitTag2 = ":";
    private void OnGlobalTouchBegin(EventContext context)
    {
        if (context.initiator is DisplayObject target && target.gOwner != null)
        {
            if (curTouchTarget >= maxTouchTargets)
            {
                touchTargets.Clear();
                curTouchTarget = 0;
            }
            touchTargets.Add(splitTag1);
            touchTargets.Add(target.gOwner.id);
            touchTargets.Add(splitTag2);
            touchTargets.Add(target.gOwner.name);
            curTouchTarget++;
        }

        // #if UNITY_EDITOR
        //         if (touchTargets.Count > 0)
        //         {
        //             string s = string.Join("", touchTargets.ToArray());
        //             Log.Info($"touchTargets:{s}");
        //         }
        // #endif
    }

    public void Report(string message, string stackTrace)
    {
        LogCallback(message, stackTrace, LogType.Error);
    }

    private void LogCallback(string condition, string stackTrace, LogType type)
    {
        if (type != LogType.Exception && type != LogType.Error)
        {
            return;
        }

        // if ((Time.realtimeSinceStartup - lastLogTime) >= 60)
        // {
        //     logCount = 0;
        //     lastLogTime = Time.realtimeSinceStartup;
        // }

        if (logCount >= maxLogCount)
        {
            return;
        }
        logCount++;
        // Platform.GetInstance().PlatformLog($"catch error condition:{condition} stackTrace:{stackTrace}");

        //get方法
        //         sb.Clear();
        //         sb.Append(url);
        //         sb.Append("&clientId=");
        //         sb.Append(Session.clientId);

        //         sb.Append("&accountId=");
        //         sb.Append(Session.accountId);

        //         sb.Append("&playerUid=");
        //         sb.Append(Session.playerUid);

        //         sb.Append("&clientTime=");
        //         sb.Append(System.DateTime.Now.ToString());

        //         sb.Append("&clientType=");
        // #if UNITY_EDITOR
        //         sb.Append("editor");
        // #else
        //         sb.Append("wxgame");
        // #endif

        //         sb.Append("&platform=");
        //         sb.Append(Platform.GetInstance().PlatformName);

        //         sb.Append("&type=log");

        //         sb.Append("&level=");
        //         sb.Append(type.ToString().ToLower());

        //         sb.Append("&message=");
        //         sb.Append(UnityWebRequest.EscapeURL(condition));

        //         sb.Append("&ui=");
        //         sb.Append(UnityWebRequest.EscapeURL(GetUIPath()));

        //         sb.Append("&stackTrace=");
        //         sb.Append(UnityWebRequest.EscapeURL(stackTrace));

        // Debug.Log(sb.ToString());
        // URLRequest.Get(sb.ToString());


        //post方法
        var data = new LogData();
        data.clientId = Session.clientId;
        data.accountId = Session.accountId.ToString();
        data.playerUid = Session.playerUid.ToString();
        data.clientTime = System.DateTime.Now.ToString();

#if UNITY_EDITOR
        data.clientType = "editor";
#elif UNITY_ANDROID
        data.clientType = "apk";
#elif UNITY_IOS
        data.clientType = "ios";
#endif
        data.platform = Platform.GetInstance().PlatformName;
        data.type = "log";
        data.level = type.ToString().ToLower();

        data.message = condition;
        data.stackTrace = stackTrace;
        data.ui = GetUIPath();
        data.version = AssetBundleManager.GetVersion();

        var log = new LogWrapper
        {
            __logs__ = new LogData[] { data }
        };
        StartCoroutine(PostLogs(log));

        var newLogData = new NewLogData
        {
            openId = Session.UserId,
            playerUid = data.playerUid,
            key = data.message,
            value = data.stackTrace,
            ui = data.ui,
            device = data.clientType,
            ver = data.version
        };
        StartCoroutine(PostGGiLog(newLogData));
    }


    private StringBuilder uiStr = new StringBuilder();
    private string GetUIPath()
    {
        uiStr.Clear();
        var count = GRoot.inst.numChildren;
        for (int i = 0; i < count; i++)
        {
            var child = GRoot.inst.GetChildAt(i);
            if (child.displayObject != null && child.displayObject.gameObject != null)
            {
                var panelName = child.displayObject.gameObject.name;
                if (!string.IsNullOrEmpty(panelName))
                {
                    uiStr.Append(panelName.Replace("Window - ", ""));
                    if (i < count - 1)
                    {
                        uiStr.Append(",");
                    }
                }
            }
        }

        uiStr.AppendLine("(");
        foreach (var item in touchTargets)
        {
            uiStr.Append(item);
        }
        uiStr.Append(")");
        return uiStr.ToString();
    }



    [System.Serializable]
    public class LogData
    {
        public string clientId;
        public string accountId;
        public string playerUid;
        public string clientTime;
        public string clientType;
        public string platform;
        public string type;
        public string level;
        public string message;
        public string stackTrace;
        public string ui;
        public string version;
    }
    [System.Serializable]
    public class LogWrapper
    {
        public LogData[] __logs__;
    }

    [System.Serializable]
    public class NewLogData
    {
        public string openId;
        public string playerUid;
        public string key;
        public string value;
        public string ui;
        public string device;
        public string ver;
    }

    private IEnumerator PostLogs(LogWrapper wrapper)
    {
        string jsonData = JsonUtility.ToJson(wrapper);
        using (UnityWebRequest request = new UnityWebRequest(postUrl, "POST"))
        {
            byte[] bodyRaw = System.Text.Encoding.UTF8.GetBytes(jsonData);
            request.uploadHandler = new UploadHandlerRaw(bodyRaw);
            request.downloadHandler = new DownloadHandlerBuffer();
            request.SetRequestHeader("Content-Type", "application/json");
            request.SetRequestHeader("x-log-apiversion", "0.6.0");
            request.SetRequestHeader("x-log-bodyrawsize", bodyRaw.Length.ToString());

            yield return request.SendWebRequest();

#if UNITY_EDITOR
            if (request.result != UnityWebRequest.Result.Success)
            {
                Debug.LogWarning($"url: {postUrl}, Error: {request.error}");
            }
            else
            {
                Debug.Log($"url: {postUrl}, Response: {request.downloadHandler.text}");
            }
#endif
        }
    }

    private IEnumerator PostGGiLog(NewLogData data)
    {
#if UNITY_EDITOR
        string url = "http://192.168.77.111:38083/webtest_hmt/errorLog";
#else
        string url = Platform.GetInstance().UrlBase+"errorLog";
#endif
        string jsonData = JsonUtility.ToJson(data);
        using (UnityWebRequest request = new UnityWebRequest(url, "POST"))
        {
            byte[] bodyRaw = System.Text.Encoding.UTF8.GetBytes(jsonData);
            request.uploadHandler = new UploadHandlerRaw(bodyRaw);
            request.downloadHandler = new DownloadHandlerBuffer();
            request.SetRequestHeader("Content-Type", "application/json");

            yield return request.SendWebRequest();

#if UNITY_EDITOR
            if (request.result != UnityWebRequest.Result.Success)
            {
                Debug.LogWarning($"url: {url}, Error: {request.error}");
            }
            else
            {
                Debug.Log($"url: {url}, Response: {request.downloadHandler.text}");
            }
#endif
        }
    }
}